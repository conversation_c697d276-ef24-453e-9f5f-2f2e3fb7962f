set -x
MODEL_PATH=Qwen/Qwen2.5-1.5B-Instruct
sp_size=2
export VLLM_ATTENTION_BACKEND=XFORMERS
export CUDA_VISIBLE_DEVICES=1,2
export NCCL_TIMEOUT=1800000

SEEDS=(1) 

max_prompt_length=$((1024 * 1))
max_response_length=$((1024 * 3))

use_dynamic_bsz=True
# actor_ppo_max_token_len=8500
# infer_ppo_max_token_len=8500
# 三卡8500

actor_ppo_max_token_len=9500
infer_ppo_max_token_len=9500
offload=True
gen_tp=1

train_batch_size=16

model_name=${MODEL_PATH##*/} 
datasets=("math500" "aime2024" "gsm8k" "math")
data_path=examples/data

for seed in "${SEEDS[@]}"; do
    echo "Running with seed: $seed"
    # 对于每一对train_file和test_file，运行下面的脚本
    for i in $(seq 3 3); do
        dataset=${datasets[$i]}
        train_file=$data_path/$dataset/train.parquet
        val_file=$data_path/$dataset/val.parquet
        test_file=$data_path/$dataset/test.parquet
        exp_name=$dataset-test

        for use_curriculum_learning in True False; do
                echo "Running with curriculum learning: $use_curriculum_learning"
                python3 -m verl.trainer.main_ppo \
                    algorithm.adv_estimator=grpo \
                    data.train_files="$train_file" \
                    data.val_files="$val_file" \
                    +data.test_files="$test_file" \
                    data.train_batch_size=${train_batch_size} \
                    data.enable_curriculum_learning=${use_curriculum_learning} \
                    +data.use_influence_functions=True \
                    +data.influence_use_kfac=True\
                    +data.influence_regularization_lambda=1e-3\
                    +data.seed=${seed} \
                    ++data.shuffle=True \
                    +data.data_source_key=cluster \
                    data.max_prompt_length="${max_prompt_length}" \
                    data.max_response_length="${max_response_length}" \
                    actor_rollout_ref.actor.use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.ref.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.rollout.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
                    actor_rollout_ref.actor.ppo_max_token_len_per_gpu=${actor_ppo_max_token_len} \
                    actor_rollout_ref.ref.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
                    actor_rollout_ref.rollout.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
                    actor_rollout_ref.actor.ulysses_sequence_parallel_size=${sp_size} \
                    actor_rollout_ref.ref.ulysses_sequence_parallel_size=${sp_size} \
                    actor_rollout_ref.model.path=$MODEL_PATH \
                    actor_rollout_ref.actor.optim.lr=1e-6 \
                    actor_rollout_ref.model.use_remove_padding=True \
                    actor_rollout_ref.actor.ppo_mini_batch_size=32 \
                    actor_rollout_ref.actor.use_kl_loss=True \
                    actor_rollout_ref.actor.kl_loss_coef=0.001 \
                    actor_rollout_ref.actor.kl_loss_type=low_var_kl \
                    actor_rollout_ref.model.enable_gradient_checkpointing=True \
                    actor_rollout_ref.actor.fsdp_config.param_offload=${offload} \
                    actor_rollout_ref.actor.fsdp_config.optimizer_offload=${offload} \
                    actor_rollout_ref.rollout.tensor_model_parallel_size=1 \
                    actor_rollout_ref.rollout.name=vllm \
                    actor_rollout_ref.rollout.gpu_memory_utilization=0.5 \
                    actor_rollout_ref.rollout.n=10 \
                    actor_rollout_ref.rollout.enforce_eager=True \
                    actor_rollout_ref.rollout.free_cache_engine=False \
                    actor_rollout_ref.rollout.enable_chunked_prefill=True \
                    actor_rollout_ref.rollout.tensor_model_parallel_size=${gen_tp} \
                    actor_rollout_ref.ref.fsdp_config.param_offload=${offload} \
                    algorithm.kl_ctrl.kl_coef=0.001 \
                    trainer.critic_warmup=0 \
                    trainer.logger=['wandb'] \
                    trainer.project_name='GRPO_combined_logic' \
                    trainer.experiment_name=${exp_name}-GRPO-${use_curriculum_learning}-${model_name} \
                    trainer.n_gpus_per_node=${sp_size} \
                    trainer.nnodes=1 \
                    trainer.default_local_dir=./local_save_dir/${exp_name}-GRPO-${use_curriculum_learning}-${model_name} \
                    trainer.default_hdfs_dir=null \
                    trainer.remove_previous_ckpt_in_save=True \
                    trainer.hf_account=Tim-Saijun \
                    trainer.cleanup_after_upload=True \
                    trainer.save_freq=100 \
                    trainer.test_freq=10 \
                    trainer.total_epochs=1 $@ 2>&1 | tee ./local_save_dir/${exp_name}-GRPO-${use_curriculum_learning}-${model_name}.log
        done
    done
done
