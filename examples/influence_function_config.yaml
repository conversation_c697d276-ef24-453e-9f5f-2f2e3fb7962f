# Example configuration for using influence functions in curriculum learning
# This configuration shows how to enable and configure influence function-based
# bandit updates instead of perplexity-based scoring

data:
  # Enable curriculum learning
  enable_curriculum_learning: true
  
  # Enable influence functions for curriculum learning
  use_influence_functions: true
  
  # Influence function parameters
  influence_use_kfac: true                    # Use K-FAC approximation for Hessian
  influence_regularization_lambda: 1e-3      # Regularization parameter λ for (H_θ + λI)^(-1)
  influence_damping_factor: 1e-3             # Damping factor for numerical stability
  influence_max_samples_per_batch: 32        # Maximum samples to process in one batch
  influence_kfac_damping: 1e-3               # K-FAC specific damping parameter
  
  # Data source configuration
  data_source_key: "data_source"             # Key in dataset that identifies data source
  
  # Alternative: automatic bucketing if no data_source_key
  auto_bucket_by_length: false               # Enable automatic length-based bucketing
  num_length_buckets: 5                      # Number of length buckets
  length_bucket_method: "quantile"           # Method for creating buckets: "quantile" or "uniform"
  
  auto_bucket_by_embedding: false            # Enable embedding-based clustering
  num_embedding_clusters: 5                  # Number of embedding clusters
  embedding_method: "sentence_transformers"  # Embedding method: "tfidf", "sentence_transformers"
  clustering_method: "kmeans"                # Clustering method: "kmeans", "hierarchical"
  embedding_model_name: "all-MiniLM-L6-v2"  # Model name for sentence transformers
  
  # Standard data configuration
  train_files: ["path/to/train.parquet"]
  val_files: ["path/to/val.parquet"]
  test_files: ["path/to/test.parquet"]
  prompt_key: "prompt"
  max_prompt_length: 512
  train_batch_size: 32
  shuffle: true
  seed: 42

# Training configuration
trainer:
  total_epochs: 3
  test_freq: 100                             # Frequency of validation runs
  save_freq: 500                             # Frequency of checkpoint saves
  critic_warmup: 100                         # Steps before actor updates begin
  
  # Logging configuration
  logger: ["wandb"]                          # Enable wandb for curriculum learning visualization
  val_generations_to_log_to_wandb: 5         # Number of validation samples to log

# Algorithm configuration
algorithm:
  adv_estimator: "gae"                       # Use GAE for advantage estimation
  gamma: 0.99                                # Discount factor
  lam: 0.95                                  # GAE lambda parameter
  kl_penalty: "kl"                           # KL penalty type
  
  # KL control
  kl_ctrl:
    type: "adaptive"                         # Adaptive KL control
    kl_coef: 0.1                            # Initial KL coefficient
    target_kl: 0.01                         # Target KL divergence
    horizon: 10000                           # Horizon for adaptive control

# Model configuration
actor_rollout_ref:
  model:
    path: "path/to/model"                    # Path to pretrained model
    use_remove_padding: true                 # Required for sequence parallelism
  
  actor:
    strategy: "fsdp"                         # Use FSDP for distributed training
    ppo_epochs: 1                            # PPO epochs per batch
    ppo_mini_batch_size: 8                   # Mini-batch size for PPO updates
    ppo_micro_batch_size_per_gpu: 2          # Micro-batch size per GPU
    use_kl_loss: false                       # Use KL penalty instead of KL loss
    
    optim:
      lr: 1e-6                              # Learning rate
      weight_decay: 0.01                     # Weight decay
      
  rollout:
    n: 4                                     # Number of rollouts per prompt
    temperature: 0.7                         # Sampling temperature
    top_p: 0.9                              # Top-p sampling
    max_new_tokens: 256                      # Maximum new tokens to generate
    
  ref:
    strategy: "fsdp"                         # Reference policy strategy

# Critic configuration (if using GAE)
critic:
  strategy: "fsdp"                           # Use FSDP for critic
  ppo_epochs: 1                              # PPO epochs for critic
  ppo_mini_batch_size: 8                     # Mini-batch size for critic
  ppo_micro_batch_size_per_gpu: 2            # Micro-batch size per GPU
  
  optim:
    lr: 1e-5                                # Critic learning rate
    weight_decay: 0.01                       # Weight decay

# Reward model configuration
reward_model:
  enable: false                              # Disable neural reward model, use rule-based

# Hardware configuration
trainer:
  n_gpus_per_node: 8                        # Number of GPUs per node
  nnodes: 1                                  # Number of nodes

# Example usage notes:
# 1. Set use_influence_functions: true to enable influence function-based curriculum learning
# 2. Adjust influence_regularization_lambda to control regularization strength
# 3. Set influence_use_kfac: false to use standard Hessian computation (slower but more accurate)
# 4. Monitor curriculum learning metrics in wandb to see influence scores and sampling weights
# 5. The system will automatically fall back to perplexity-based scoring if influence computation fails
