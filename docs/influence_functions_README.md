# Influence Function-Based Curriculum Learning

This document describes the implementation of influence function-based bandit updates for curriculum learning, replacing the previous perplexity-based approach.

## Overview

Influence functions provide a principled way to measure how individual training samples affect model performance on a validation set. This implementation uses influence functions to guide curriculum learning by computing how beneficial or harmful each training sample is for the model's performance.

### Mathematical Foundation

The influence function measures how training sample `z` affects the loss on validation set `D_r`:

```
I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)
```

Where:
- `I_θ(D_r, z)` is the influence function
- `L(θ, D_r)` is the loss function evaluated on validation set `D_r`
- `L(θ, z)` is the loss function evaluated on training sample `z`
- `θ` represents the model parameters
- `H_θ` is the Hessian matrix of the loss with respect to parameters
- `λ` is a regularization parameter
- `I` is the identity matrix

## Key Components

### 1. Influence Function Calculator (`verl/utils/influence_functions.py`)

The core implementation includes:

- **InfluenceFunctionCalculator**: Basic influence function computation
- **KFACInfluenceFunctionCalculator**: K-FAC accelerated version
- **create_influence_calculator**: Factory function for creating calculators

#### Features:
- Efficient gradient computation
- Hessian-vector product computation
- Conjugate gradient solver for inverse Hessian
- K-FAC approximation for scalability

### 2. K-FAC Implementation (`verl/utils/kfac.py`)

Kronecker-Factored Approximate Curvature (K-FAC) makes Hessian computation tractable:

- **KFACLayer**: Per-layer K-FAC statistics
- **KFACOptimizer**: Model-wide K-FAC management
- Kronecker product approximation: `H ≈ A ⊗ G`

### 3. Curriculum Learning Integration

Simplified `LearnabilityEstimator` with influence function support:

- Primary method: Influence function scores when enabled and available
- Fallback method: Original reward-advantage formula for backward compatibility
- Removed perplexity-based scoring as intermediate fallback
- Configurable influence function parameters

## Configuration

Enable influence functions in your configuration:

```yaml
data:
  # Enable curriculum learning with influence functions
  enable_curriculum_learning: true
  use_influence_functions: true
  
  # Influence function parameters
  influence_use_kfac: true                    # Use K-FAC approximation
  influence_regularization_lambda: 1e-3      # Regularization parameter λ
  influence_damping_factor: 1e-3             # Numerical stability damping
  influence_max_samples_per_batch: 32        # Batch size for computation
  influence_kfac_damping: 1e-3               # K-FAC specific damping
```

## Usage Example

```python
from verl.utils.influence_functions import create_influence_calculator
from verl.utils.curriculum_learning import CurriculumSampler

# Create influence calculator
influence_config = {
    'use_kfac': True,
    'regularization_lambda': 1e-3,
    'damping_factor': 1e-3
}
calculator = create_influence_calculator(model, influence_config)

# Set up curriculum learning
sampler = CurriculumSampler(dataset, data_source_key='data_source')
sampler.enable_influence_functions(True)

# Update with influence scores
sampler.update_weights(
    source_rewards=rewards,
    source_advantages=advantages,
    source_influence_scores=influence_scores  # New parameter
)
```

## Performance Considerations

### Computational Complexity

1. **Standard Influence Functions**: O(p²) where p is the number of parameters
2. **K-FAC Approximation**: O(p) with Kronecker factorization
3. **Memory Usage**: Scales with model size and batch size

### Optimization Strategies

1. **Use K-FAC**: Enable `influence_use_kfac: true` for large models
2. **Batch Processing**: Limit `influence_max_samples_per_batch`
3. **Caching**: Validation gradients are cached for efficiency
4. **Fallback**: Automatic fallback to perplexity-based methods

## Monitoring and Debugging

### Metrics to Track

- `curriculum/mean_influence_score/{source}`: Average influence score per source
- `curriculum/use_influence_functions/{source}`: Whether influence functions are active for this source
- `curriculum/sampling_weight/{source}`: Current sampling weights
- `curriculum/expected_value/{source}`: Expected value from influence functions
- `curriculum/influence_functions_active_ratio`: Ratio of sources using influence functions
- `curriculum/influence_functions_enabled`: Whether influence functions are enabled globally
- `curriculum/mean_influence_score_overall`: Overall mean influence score across all active sources

### Common Issues

1. **Numerical Instability**: Increase `influence_damping_factor`
2. **Memory Issues**: Reduce `influence_max_samples_per_batch`
3. **Slow Computation**: Enable K-FAC with `influence_use_kfac: true`

## Implementation Details

### Gradient Computation

```python
def compute_gradients(self, batch, loss_fn):
    self.model.zero_grad()
    outputs = self.model(**batch)
    loss = loss_fn(outputs, batch)
    loss.backward()
    
    gradients = {}
    for name, param in self.model.named_parameters():
        if param.grad is not None:
            gradients[name] = param.grad.clone().detach()
    return gradients
```

### K-FAC Approximation

For linear layers, the Hessian is approximated as:
```
H ≈ A ⊗ G
```
Where:
- `A = E[aa^T]` is the input covariance matrix
- `G = E[∇_s L ∇_s L^T]` is the output gradient covariance matrix

### Influence Score Integration

The influence scores are integrated into the curriculum learning system through the `LearnabilityEstimator.expected_value` property:

```python
@property
def expected_value(self) -> float:
    # Use influence function scores when available and enabled
    if self.use_influence_functions and self.recent_influence_scores:
        influence_score = self.mean_influence_score
        # Apply sigmoid transformation to map to (0, 1) range
        normalized_score = 1.0 / (1.0 + np.exp(-influence_score))
        return normalized_score

    # Fall back to the original reward-advantage formula
    else:
        reward_rate = self.alpha / (self.alpha + self.beta)
        return reward_rate * self.mu
```

## Testing

Run the test suite:

```bash
python -m pytest tests/test_influence_functions.py -v
```

Test coverage includes:
- Basic influence function computation
- K-FAC layer operations
- Curriculum learning integration
- Error handling and fallbacks

## Examples

See the following files for complete examples:
- `examples/influence_function_example.py`: Basic usage demonstration
- `examples/influence_function_config.yaml`: Configuration template
- `tests/test_influence_functions.py`: Comprehensive test suite

## Future Improvements

1. **Distributed Computation**: Parallelize influence computation across GPUs
2. **Approximation Methods**: Implement additional Hessian approximations
3. **Caching Strategies**: More sophisticated gradient caching
4. **Online Updates**: Incremental influence score updates

## References

1. Koh, P. W., & Liang, P. (2017). Understanding black-box predictions via influence functions.
2. Martens, J., & Grosse, R. (2015). Optimizing neural networks with kronecker-factored approximate curvature.
3. Grosse, R., & Martens, J. (2016). A kronecker-factored approximate fisher matrix for convolution layers.
