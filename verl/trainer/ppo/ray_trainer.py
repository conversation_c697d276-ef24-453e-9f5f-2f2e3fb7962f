# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""
FSDP PPO Trainer with Ray-based single controller.
This trainer supports model-agonistic model initialization with huggingface
"""

import os
import uuid
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum
from pprint import pprint
from typing import Type, Dict
from copy import deepcopy

import numpy as np
from codetiming import Timer
from omegaconf import OmegaConf, open_dict
from verl import DataProto
from verl.protocol import pad_dataproto_to_divisor, unpad_dataproto
from verl.single_controller.base import Worker
from verl.single_controller.ray import RayResourcePool, RayWorkerGroup, RayClassWithInitArgs
from verl.single_controller.ray.base import create_colocated_worker_cls
from verl.trainer.ppo import core_algos
from verl.utils.seqlen_balancing import get_seqlen_balanced_partitions, log_seqlen_unbalance
from verl.utils.checkpoint.checkpoint_manager import find_latest_ckpt_path
from verl.utils.dataset.rl_dataset import RLHFDataset, collate_fn

WorkerType = Type[Worker]


class Role(Enum):
    """
    To create more roles dynamically, you can subclass Role and add new members
    """
    Actor = 0
    Rollout = 1
    ActorRollout = 2
    Critic = 3
    RefPolicy = 4
    RewardModel = 5
    ActorRolloutRef = 6


@dataclass
class ResourcePoolManager:
    """
    Define a resource pool specification. Resource pool will be initialized first.
    Mapping
    """
    resource_pool_spec: dict[str, list[int]]
    mapping: dict[Role, str]
    resource_pool_dict: dict[str, RayResourcePool] = field(default_factory=dict)

    def create_resource_pool(self):
        for resource_pool_name, process_on_nodes in self.resource_pool_spec.items():
            # max_colocate_count means the number of WorkerGroups (i.e. processes) in each RayResourcePool
            # For FSDP backend, we recommend using max_colocate_count=1 that merge all WorkerGroups into one.
            # For Megatron backend, we recommend using max_colocate_count>1 that can utilize different WorkerGroup for differnt models
            resource_pool = RayResourcePool(process_on_nodes=process_on_nodes,
                                            use_gpu=True,
                                            max_colocate_count=1,
                                            name_prefix=resource_pool_name)
            self.resource_pool_dict[resource_pool_name] = resource_pool

    def get_resource_pool(self, role: Role) -> RayResourcePool:
        """Get the resource pool of the worker_cls"""
        return self.resource_pool_dict[self.mapping[role]]


import torch
from verl.utils.torch_functional import masked_mean
from verl.utils.influence_functions import create_influence_calculator


def apply_kl_penalty(data: DataProto, kl_ctrl: core_algos.AdaptiveKLController, kl_penalty='kl'):
    responses = data.batch['responses']
    response_length = responses.size(1)
    token_level_scores = data.batch['token_level_scores']
    batch_size = data.batch.batch_size[0]
    attention_mask = data.batch['attention_mask']
    response_mask = attention_mask[:, -response_length:]

    # compute kl between ref_policy and current policy
    if 'ref_log_prob' in data.batch.keys():
        kld = core_algos.kl_penalty(data.batch['old_log_probs'], data.batch['ref_log_prob'],
                                    kl_penalty=kl_penalty)  # (batch_size, response_length)
        kld = kld * response_mask
        beta = kl_ctrl.value
    else:
        beta = 0
        kld = torch.zeros_like(response_mask, dtype=torch.float32)

    token_level_rewards = token_level_scores - beta * kld

    current_kl = masked_mean(kld, mask=response_mask, axis=-1)  # average over sequence
    current_kl = torch.mean(current_kl, dim=0).item()

    # according to https://github.com/huggingface/trl/blob/951ca1841f29114b969b57b26c7d3e80a39f75a0/trl/trainer/ppo_trainer.py#L837
    kl_ctrl.update(current_kl=current_kl, n_steps=batch_size)
    data.batch['token_level_rewards'] = token_level_rewards

    metrics = {'critic/kl': current_kl, 'critic/kl_coeff': beta}

    return data, metrics


def compute_advantage(data: DataProto, adv_estimator, gamma=1.0, lam=1.0, num_repeat=1):
    # prepare response group
    # TODO: add other ways to estimate advantages
    if adv_estimator == 'gae':
        values = data.batch['values']
        responses = data.batch['responses']
        response_length = responses.size(-1)
        attention_mask = data.batch['attention_mask']
        response_mask = attention_mask[:, -response_length:]
        token_level_rewards = data.batch['token_level_rewards']
        advantages, returns = core_algos.compute_gae_advantage_return(token_level_rewards=token_level_rewards,
                                                                      values=values,
                                                                      eos_mask=response_mask,
                                                                      gamma=gamma,
                                                                      lam=lam)
        data.batch['advantages'] = advantages
        data.batch['returns'] = returns
    elif adv_estimator == 'grpo':
        token_level_rewards = data.batch['token_level_rewards']
        index = data.non_tensor_batch['uid']
        responses = data.batch['responses']
        response_length = responses.size(-1)
        attention_mask = data.batch['attention_mask']
        response_mask = attention_mask[:, -response_length:]
        advantages, returns = core_algos.compute_grpo_outcome_advantage(token_level_rewards=token_level_rewards,
                                                                        eos_mask=response_mask,
                                                                        index=index)
        data.batch['advantages'] = advantages
        data.batch['returns'] = returns
    elif adv_estimator == 'reinforce_plus_plus':
        token_level_rewards = data.batch['token_level_rewards']
        responses = data.batch['responses']
        response_length = responses.size(-1)
        attention_mask = data.batch['attention_mask']
        response_mask = attention_mask[:, -response_length:]
        advantages, returns = core_algos.compute_reinforce_plus_plus_outcome_advantage(
            token_level_rewards=token_level_rewards, eos_mask=response_mask, gamma=gamma)
        data.batch['advantages'] = advantages
        data.batch['returns'] = returns
    elif adv_estimator == 'remax':
        token_level_rewards = data.batch['token_level_rewards']
        index = data.non_tensor_batch['uid']
        responses = data.batch['responses']
        response_length = responses.size(-1)
        attention_mask = data.batch['attention_mask']
        response_mask = attention_mask[:, -response_length:]

        reward_baselines = data.batch['reward_baselines']

        advantages, returns = core_algos.compute_remax_outcome_advantage(token_level_rewards=token_level_rewards,
                                                                         reward_baselines=reward_baselines,
                                                                         eos_mask=response_mask)

        data.batch['advantages'] = advantages
        data.batch['returns'] = returns
    elif adv_estimator == 'rloo':
        token_level_rewards = data.batch['token_level_rewards']
        index = data.non_tensor_batch['uid']
        responses = data.batch['responses']
        response_length = responses.size(-1)
        attention_mask = data.batch['attention_mask']
        response_mask = attention_mask[:, -response_length:]
        advantages, returns = core_algos.compute_rloo_outcome_advantage(token_level_rewards=token_level_rewards,
                                                                        eos_mask=response_mask,
                                                                        index=index)
        data.batch['advantages'] = advantages
        data.batch['returns'] = returns
    else:
        raise NotImplementedError
    return data


def reduce_metrics(metrics: dict):
    for key, val in metrics.items():
        metrics[key] = np.mean(val)
    return metrics


def _compute_response_info(batch):
    response_length = batch.batch['responses'].shape[-1]

    prompt_mask = batch.batch['attention_mask'][:, :-response_length]
    response_mask = batch.batch['attention_mask'][:, -response_length:]

    prompt_length = prompt_mask.sum(-1).float()
    response_length = response_mask.sum(-1).float()  # (batch_size,)

    return dict(
        response_mask=response_mask,
        prompt_length=prompt_length,
        response_length=response_length,
    )


def calculate_log_perplexity(log_probs, mask):
    """Calculate log perplexity from log probabilities.

    Args:
        log_probs: Log probabilities tensor [batch_size, seq_len]
        mask: Boolean mask for valid tokens [batch_size, seq_len]

    Returns:
        Log perplexity for each sequence in the batch
    """
    # Sum log probabilities for each sequence (only for masked positions)
    masked_log_probs = log_probs * mask.float()
    sum_log_probs = torch.sum(masked_log_probs, dim=-1)

    # Count number of tokens in each sequence
    token_counts = torch.sum(mask.float(), dim=-1)

    # Calculate log perplexity: -1 * average log probability
    # Higher log perplexity = more difficult/uncertain predictions
    log_ppl = -1 * sum_log_probs / torch.clamp(token_counts, min=1.0)

    # print(f"log_ppl: {log_ppl}")

    return log_ppl

def compute_data_metrics(batch, use_critic=True):
    sequence_score = batch.batch['token_level_scores'].sum(-1)
    sequence_reward = batch.batch['token_level_rewards'].sum(-1)

    advantages = batch.batch['advantages']
    returns = batch.batch['returns']

    max_response_length = batch.batch['responses'].shape[-1]

    prompt_mask = batch.batch['attention_mask'][:, :-max_response_length].bool()
    response_mask = batch.batch['attention_mask'][:, -max_response_length:].bool()

    max_prompt_length = prompt_mask.size(-1)

    response_info = _compute_response_info(batch)
    prompt_length = response_info['prompt_length']
    response_length = response_info['response_length']

    valid_adv = torch.masked_select(advantages, response_mask)
    valid_returns = torch.masked_select(returns, response_mask)

    # Calculate log perplexity if old_log_probs are available
    log_ppl = None
    if 'old_log_probs' in batch.batch:
        log_probs = batch.batch['old_log_probs']
        log_ppl = calculate_log_perplexity(log_probs, response_mask)

    if use_critic:
        values = batch.batch['values']
        valid_values = torch.masked_select(values, response_mask)
        return_diff_var = torch.var(valid_returns - valid_values)
        return_var = torch.var(valid_returns)

    metrics = {
        # score
        'critic/score/mean':
            torch.mean(sequence_score).detach().item(),
        'critic/score/max':
            torch.max(sequence_score).detach().item(),
        'critic/score/min':
            torch.min(sequence_score).detach().item(),
        # reward
        'critic/rewards/mean':
            torch.mean(sequence_reward).detach().item(),
        'critic/rewards/max':
            torch.max(sequence_reward).detach().item(),
        'critic/rewards/min':
            torch.min(sequence_reward).detach().item(),
        # adv
        'critic/advantages/mean':
            torch.mean(valid_adv).detach().item(),
        'critic/advantages/max':
            torch.max(valid_adv).detach().item(),
        'critic/advantages/min':
            torch.min(valid_adv).detach().item(),
        # returns
        'critic/returns/mean':
            torch.mean(valid_returns).detach().item(),
        'critic/returns/max':
            torch.max(valid_returns).detach().item(),
        'critic/returns/min':
            torch.min(valid_returns).detach().item(),
        # log perplexity metrics if available
        **({} if log_ppl is None else {
            'critic/log_ppl/mean': torch.mean(log_ppl).detach().item(),
            'critic/log_ppl/max': torch.max(log_ppl).detach().item(),
            'critic/log_ppl/min': torch.min(log_ppl).detach().item(),
        }),
        **({
            # values
            'critic/values/mean': torch.mean(valid_values).detach().item(),
            'critic/values/max': torch.max(valid_values).detach().item(),
            'critic/values/min': torch.min(valid_values).detach().item(),
            # vf explained var
            'critic/vf_explained_var': (1.0 - return_diff_var / (return_var + 1e-5)).detach().item(),
        } if use_critic else {}),

        # response length
        'response_length/mean':
            torch.mean(response_length).detach().item(),
        'response_length/max':
            torch.max(response_length).detach().item(),
        'response_length/min':
            torch.min(response_length).detach().item(),
        'response_length/clip_ratio':
            torch.mean(torch.eq(response_length, max_response_length).float()).detach().item(),
        # prompt length
        'prompt_length/mean':
            torch.mean(prompt_length).detach().item(),
        'prompt_length/max':
            torch.max(prompt_length).detach().item(),
        'prompt_length/min':
            torch.min(prompt_length).detach().item(),
        'prompt_length/clip_ratio':
            torch.mean(torch.eq(prompt_length, max_prompt_length).float()).detach().item(),
    }

    return metrics


def compute_timing_metrics(batch, timing_raw):
    response_info = _compute_response_info(batch)
    num_prompt_tokens = torch.sum(response_info['prompt_length']).item()
    num_response_tokens = torch.sum(response_info['response_length']).item()
    num_overall_tokens = num_prompt_tokens + num_response_tokens

    num_tokens_of_section = {
        'gen': num_response_tokens,
        **{
            name: num_overall_tokens for name in ['ref', 'values', 'adv', 'update_critic', 'update_actor']
        },
    }

    return {
        **{
            f'timing_s/{name}': value for name, value in timing_raw.items()
        },
        **{
            f'timing_per_token_ms/{name}': timing_raw[name] * 1000 / num_tokens_of_section[name] for name in set(num_tokens_of_section.keys(
            )) & set(timing_raw.keys())
        },
    }


@contextmanager
def _timer(name: str, timing_raw: Dict[str, float]):
    with Timer(name=name, logger=None) as timer:
        yield
    timing_raw[name] = timer.last


class RayPPOTrainer(object):
    """
    Note that this trainer runs on the driver process on a single CPU/GPU node.
    """

    # TODO: support each role have individual ray_worker_group_cls,
    # i.e., support different backend of different role
    def __init__(self,
                 config,
                 tokenizer,
                 role_worker_mapping: dict[Role, WorkerType],
                 resource_pool_manager: ResourcePoolManager,
                 ray_worker_group_cls: RayWorkerGroup = RayWorkerGroup,
                 reward_fn=None,
                 val_reward_fn=None):

        # assert torch.cuda.is_available(), 'cuda must be available on driver'

        self.tokenizer = tokenizer
        self.config = config
        self.reward_fn = reward_fn
        self.val_reward_fn = val_reward_fn

        self.hybrid_engine = config.actor_rollout_ref.hybrid_engine
        assert self.hybrid_engine, 'Currently, only support hybrid engine'

        if self.hybrid_engine:
            assert Role.ActorRollout in role_worker_mapping, f'{role_worker_mapping.keys()=}'

        self.role_worker_mapping = role_worker_mapping
        self.resource_pool_manager = resource_pool_manager
        self.use_reference_policy = Role.RefPolicy in role_worker_mapping
        self.use_rm = Role.RewardModel in role_worker_mapping
        self.ray_worker_group_cls = ray_worker_group_cls

        # define KL control
        if self.use_reference_policy:
            if config.algorithm.kl_ctrl.type == 'fixed':
                self.kl_ctrl = core_algos.FixedKLController(kl_coef=config.algorithm.kl_ctrl.kl_coef)
            elif config.algorithm.kl_ctrl.type == 'adaptive':
                assert config.algorithm.kl_ctrl.horizon > 0, f'horizon must be larger than 0. Got {config.critic.kl_ctrl.horizon}'
                self.kl_ctrl = core_algos.AdaptiveKLController(init_kl_coef=config.algorithm.kl_ctrl.kl_coef,
                                                               target_kl=config.algorithm.kl_ctrl.target_kl,
                                                               horizon=config.algorithm.kl_ctrl.horizon)
            else:
                raise NotImplementedError
        else:
            self.kl_ctrl = core_algos.FixedKLController(kl_coef=0.)

        if self.config.algorithm.adv_estimator == 'gae':
            self.use_critic = True
        elif self.config.algorithm.adv_estimator in ['grpo', 'reinforce_plue_plus', 'remax', 'rloo']:
            self.use_critic = False
        else:
            raise NotImplementedError

        self._validate_config()
        self._create_dataloader()

    def _validate_config(self):
        config = self.config
        # number of GPUs total
        n_gpus = config.trainer.n_gpus_per_node * config.trainer.nnodes

        # 1. Check total batch size for data correctness
        real_train_batch_size = config.data.train_batch_size * config.actor_rollout_ref.rollout.n
        assert real_train_batch_size % n_gpus == 0, \
            f"real_train_batch_size ({real_train_batch_size}) must be divisible by total n_gpus ({n_gpus})."

        # A helper function to check "micro_batch_size" vs "micro_batch_size_per_gpu"
        # We throw an error if the user sets both. The new convention is "..._micro_batch_size_per_gpu".
        def check_mutually_exclusive(mbs, mbs_per_gpu, name: str):
            if mbs is None and mbs_per_gpu is None:
                raise ValueError(f"[{name}] Please set at least one of '{name}.micro_batch_size' or "
                                 f"'{name}.micro_batch_size_per_gpu'.")

            if mbs is not None and mbs_per_gpu is not None:
                raise ValueError(f"[{name}] You have set both '{name}.micro_batch_size' AND "
                                 f"'{name}.micro_batch_size_per_gpu'. Please remove '{name}.micro_batch_size' "
                                 f"because only '*_micro_batch_size_per_gpu' is supported (the former is deprecated).")

        if not config.actor_rollout_ref.actor.use_dynamic_bsz:
            # actor: ppo_micro_batch_size vs. ppo_micro_batch_size_per_gpu
            check_mutually_exclusive(config.actor_rollout_ref.actor.ppo_micro_batch_size,
                                     config.actor_rollout_ref.actor.ppo_micro_batch_size_per_gpu,
                                     "actor_rollout_ref.actor")

            # reference: log_prob_micro_batch_size vs. log_prob_micro_batch_size_per_gpu
            check_mutually_exclusive(config.actor_rollout_ref.ref.log_prob_micro_batch_size,
                                     config.actor_rollout_ref.ref.log_prob_micro_batch_size_per_gpu,
                                     "actor_rollout_ref.ref")

            #  The rollout section also has log_prob_micro_batch_size vs. log_prob_micro_batch_size_per_gpu
            check_mutually_exclusive(config.actor_rollout_ref.rollout.log_prob_micro_batch_size,
                                     config.actor_rollout_ref.rollout.log_prob_micro_batch_size_per_gpu,
                                     "actor_rollout_ref.rollout")

        if self.use_critic and not config.critic.use_dynamic_bsz:
            # Check for critic micro-batch size conflicts
            check_mutually_exclusive(config.critic.ppo_micro_batch_size, config.critic.ppo_micro_batch_size_per_gpu,
                                     "critic")

        # Check for reward model micro-batch size conflicts
        if config.reward_model.enable and not config.reward_model.use_dynamic_bsz:
            check_mutually_exclusive(config.reward_model.micro_batch_size, config.reward_model.micro_batch_size_per_gpu,
                                     "reward_model")

        # Actor
        # if NOT dynamic_bsz, we must ensure:
        #    ppo_mini_batch_size is divisible by ppo_micro_batch_size
        #    ppo_micro_batch_size * sequence_parallel_size >= n_gpus
        if not config.actor_rollout_ref.actor.use_dynamic_bsz:
            sp_size = config.actor_rollout_ref.actor.get('ulysses_sequence_parallel_size', 1)
            if config.actor_rollout_ref.actor.ppo_micro_batch_size is not None:
                assert config.actor_rollout_ref.actor.ppo_mini_batch_size % config.actor_rollout_ref.actor.ppo_micro_batch_size == 0
                assert config.actor_rollout_ref.actor.ppo_micro_batch_size * sp_size >= n_gpus

        # critic
        if self.use_critic and not config.critic.use_dynamic_bsz:
            sp_size = config.critic.get('ulysses_sequence_parallel_size', 1)
            if config.critic.ppo_micro_batch_size is not None:
                assert config.critic.ppo_mini_batch_size % config.critic.ppo_micro_batch_size == 0
                assert config.critic.ppo_micro_batch_size * sp_size >= n_gpus

        # Check if use_remove_padding is enabled when using sequence parallelism for fsdp
        if config.actor_rollout_ref.actor.strategy == 'fsdp':
            if config.actor_rollout_ref.actor.get('ulysses_sequence_parallel_size', 1) > 1 or \
                    config.actor_rollout_ref.ref.get('ulysses_sequence_parallel_size', 1) > 1:
                assert config.actor_rollout_ref.model.use_remove_padding, \
                    "When using sequence parallelism for actor/ref policy, you must enable `use_remove_padding`."

        if self.use_critic and config.critic.strategy == 'fsdp':
            if config.critic.get('ulysses_sequence_parallel_size', 1) > 1:
                assert config.critic.model.use_remove_padding, \
                    "When using sequence parallelism for critic, you must enable `use_remove_padding`."

        if config.data.get('val_batch_size', None) is not None:
            print(
                f"WARNING: val_batch_size is deprecated. Validation datasets are sent to inference engines as a whole batch, which will schedule the memory themselves."
            )

        print("[validate_config] All configuration checks passed successfully!")

    def _create_dataloader(self):
        from torch.utils.data import DataLoader, RandomSampler, SequentialSampler
        import torch
        from tqdm.auto import tqdm
        # TODO: we have to make sure the batch size is divisible by the dp size

        # Check if curriculum learning is enabled
        enable_curriculum_learning = self.config.data.get('enable_curriculum_learning', False)
        data_source_key = self.config.data.get('data_source_key', None)

        # Length-based bucketing options
        auto_bucket_by_length = self.config.data.get('auto_bucket_by_length', False)
        num_length_buckets = self.config.data.get('num_length_buckets', 5)
        length_bucket_method = self.config.data.get('length_bucket_method', 'quantile')

        # Embedding-based clustering options
        auto_bucket_by_embedding = self.config.data.get('auto_bucket_by_embedding', False)
        num_embedding_clusters = self.config.data.get('num_embedding_clusters', 5)
        embedding_method = self.config.data.get('embedding_method', 'tfidf')
        clustering_method = self.config.data.get('clustering_method', 'kmeans')
        embedding_model_name = self.config.data.get('embedding_model_name', None)

        self.train_dataset = RLHFDataset(parquet_files=self.config.data.train_files,
                                         tokenizer=self.tokenizer,
                                         prompt_key=self.config.data.prompt_key,
                                         max_prompt_length=self.config.data.max_prompt_length,
                                         filter_prompts=True,
                                         return_raw_chat=self.config.data.get('return_raw_chat', False),
                                         truncation='error',
                                         data_source_key=data_source_key,
                                         auto_bucket_by_length=auto_bucket_by_length,
                                         num_length_buckets=num_length_buckets,
                                         length_bucket_method=length_bucket_method,
                                         auto_bucket_by_embedding=auto_bucket_by_embedding,
                                         num_embedding_clusters=num_embedding_clusters,
                                         embedding_method=embedding_method,
                                         clustering_method=clustering_method,
                                         embedding_model_name=embedding_model_name,
                                         run_diagnostics=enable_curriculum_learning)

        # Create progress bar for training
        self.progress_bar = None
        if self.config.trainer.get('use_progress_bar', True):
            total_steps = self.config.trainer.total_epochs * (len(self.train_dataset) // self.config.data.train_batch_size)
            self.progress_bar = tqdm(total=total_steps,
                                   desc="Training Progress",
                                   dynamic_ncols=True)

        # If curriculum learning is enabled but no data_source_key is specified, use automatic bucketing
        if enable_curriculum_learning and not data_source_key:
            if auto_bucket_by_length:
                print(f"Curriculum learning enabled with automatic length bucketing (buckets: {num_length_buckets}, method: {length_bucket_method})")
            elif auto_bucket_by_embedding:
                print(f"Curriculum learning enabled with embedding-based clustering (clusters: {num_embedding_clusters}, method: {embedding_method})")
            else:
                print("Warning: Curriculum learning is enabled but data_source_key is not specified and neither auto_bucket_by_length nor auto_bucket_by_embedding is enabled. Disabling curriculum learning.")
                enable_curriculum_learning = False

        if enable_curriculum_learning:
            if data_source_key:
                print(f"Curriculum learning enabled with data_source_key: {data_source_key}")
            elif auto_bucket_by_length:
                print(f"Curriculum learning enabled with automatic length bucketing")
            elif auto_bucket_by_embedding:
                print(f"Curriculum learning enabled with embedding-based clustering")

        # Create curriculum sampler if enabled
        curriculum_sampler = None
        if enable_curriculum_learning:
            curriculum_sampler = self.train_dataset.create_curriculum_sampler(
                batch_size=self.config.data.train_batch_size,
                seed=self.config.data.get('seed', 1)
            )

            if curriculum_sampler is None:
                print("Warning: Failed to create curriculum sampler. Falling back to standard sampling.")
                enable_curriculum_learning = False
            else:
                print("Successfully created curriculum sampler")
                # Store curriculum sampler for later use
                self.curriculum_sampler = curriculum_sampler

                # Initialize influence function calculator if enabled
                self.influence_calculator = None
                if self.config.data.get('use_influence_functions', False):
                    print("Initializing influence function calculator for curriculum learning")
                    # Enable influence functions in curriculum sampler
                    self.curriculum_sampler.enable_influence_functions(True)
                    
                    # Note: Model will be set later when worker groups are created
                    self.influence_config = {
                        'use_kfac': self.config.data.get('influence_use_kfac', True),
                        'regularization_lambda': self.config.data.get('influence_regularization_lambda', 1e-3),
                        'damping_factor': self.config.data.get('influence_damping_factor', 1e-3),
                        'max_samples_per_batch': self.config.data.get('influence_max_samples_per_batch', 32),
                        'kfac_damping': self.config.data.get('influence_kfac_damping', 1e-3)
                    }

        # use sampler for better ckpt resume
        if enable_curriculum_learning:
            # When using curriculum learning, we use the curriculum sampler
            sampler = self.curriculum_sampler
            drop_last=True
        elif self.config.data.shuffle:
            train_dataloader_generator = torch.Generator()
            train_dataloader_generator.manual_seed(self.config.data.get('seed', 1))
            sampler = RandomSampler(data_source=self.train_dataset, replacement=True, generator=train_dataloader_generator)
            drop_last=True
        else:
            sampler = SequentialSampler(data_source=self.train_dataset)
            drop_last=True

        print("len(self.train_dataset): ",len(self.train_dataset))
        self.train_dataloader = DataLoader(dataset=self.train_dataset,
                                           batch_size=self.config.data.train_batch_size,
                                           drop_last=drop_last,
                                           collate_fn=collate_fn,
                                           sampler=sampler)

        # For validation dataset, we use the same parameters for consistency
        self.val_dataset = RLHFDataset(parquet_files=self.config.data.val_files,
                                       tokenizer=self.tokenizer,
                                       prompt_key=self.config.data.prompt_key,
                                       max_prompt_length=self.config.data.max_prompt_length,
                                       filter_prompts=True,
                                       return_raw_chat=self.config.data.get('return_raw_chat', False),
                                       truncation='error',
                                       data_source_key=data_source_key,
                                       auto_bucket_by_length=auto_bucket_by_length,
                                       num_length_buckets=num_length_buckets,
                                       length_bucket_method=length_bucket_method,
                                       auto_bucket_by_embedding=auto_bucket_by_embedding,
                                       num_embedding_clusters=num_embedding_clusters,
                                       embedding_method=embedding_method,
                                       clustering_method=clustering_method,
                                       embedding_model_name=embedding_model_name,
                                       run_diagnostics=enable_curriculum_learning)  # Include data_source_key for logging and enable diagnostics

        val_dataloader_generator = torch.Generator()
        val_dataloader_generator.manual_seed(self.config.data.get('seed', 1))
        self.val_dataloader = DataLoader(
            dataset=self.val_dataset,
            # Validation datasets are sent to inference engines as a whole batch,
            # which will schedule the memory themselves.
            batch_size=len(self.val_dataset),
            shuffle=True,
            generator=val_dataloader_generator,
            drop_last=False,
            collate_fn=collate_fn)

        # Create test dataset if test_files is provided in config
        self.test_dataloader = None
        if hasattr(self.config.data, 'test_files') and self.config.data.test_files:
            print(f"Creating test dataset from {self.config.data.test_files}")
            self.test_dataset = RLHFDataset(parquet_files=self.config.data.test_files,
                                           tokenizer=self.tokenizer,
                                           prompt_key=self.config.data.prompt_key,
                                           max_prompt_length=self.config.data.max_prompt_length,
                                           filter_prompts=True,
                                           return_raw_chat=self.config.data.get('return_raw_chat', False),
                                           truncation='error',
                                           data_source_key=data_source_key,
                                           auto_bucket_by_length=auto_bucket_by_length,
                                           num_length_buckets=num_length_buckets,
                                           length_bucket_method=length_bucket_method,
                                           auto_bucket_by_embedding=auto_bucket_by_embedding,
                                           num_embedding_clusters=num_embedding_clusters,
                                           embedding_method=embedding_method,
                                           clustering_method=clustering_method,
                                           embedding_model_name=embedding_model_name,
                                           run_diagnostics=enable_curriculum_learning)

            test_dataloader_generator = torch.Generator()
            test_dataloader_generator.manual_seed(self.config.data.get('seed', 1))
            self.test_dataloader = DataLoader(
                dataset=self.test_dataset,
                # Test datasets are sent to inference engines as a whole batch,
                # which will schedule the memory themselves.
                batch_size=len(self.test_dataset),
                shuffle=True,
                generator=test_dataloader_generator,
                drop_last=False,
                collate_fn=collate_fn)
            print(f'Size of test dataloader: {len(self.test_dataloader)}')
            assert len(self.test_dataloader) >= 1

        print(f'Size of train dataloader: {len(self.train_dataloader)}')
        print(f'Size of val dataloader: {len(self.val_dataloader)}')

        assert len(self.train_dataloader) >= 1
        assert len(self.val_dataloader) >= 1

        # inject total_training_steps to actor/critic optim_config. This is hacky.
        total_training_steps = len(self.train_dataloader) * self.config.trainer.total_epochs

        if self.config.trainer.total_training_steps is not None:
            total_training_steps = self.config.trainer.total_training_steps

        self.total_training_steps = total_training_steps
        print(f'Total training steps: {self.total_training_steps}')

        OmegaConf.set_struct(self.config, True)
        with open_dict(self.config):
            self.config.actor_rollout_ref.actor.optim.total_training_steps = total_training_steps
            self.config.critic.optim.total_training_steps = total_training_steps

    def _get_validation_batch_for_influence(self):
        """Get a small validation batch for influence function computation."""
        if not hasattr(self, '_cached_val_batch'):
            # Get a small sample from validation dataset
            val_sample_size = min(8, len(self.val_dataset))  # Use small batch for efficiency
            val_indices = torch.randperm(len(self.val_dataset))[:val_sample_size]

            val_samples = [self.val_dataset[i] for i in val_indices]
            from verl.utils.dataset.rl_dataset import collate_fn
            val_batch_dict = collate_fn(val_samples)

            # Convert to the format expected by influence function calculator
            self._cached_val_batch = {
                'input_ids': val_batch_dict['input_ids'],
                'attention_mask': val_batch_dict['attention_mask']
            }

        return self._cached_val_batch

    def _maybe_log_generations_to_wandb(self, inputs, outputs, scores, prefix="val"):
        """Log a table of validation or test samples to wandb

        Args:
            inputs: List of input texts
            outputs: List of generated outputs
            scores: List of scores for each generation
            prefix: Prefix for the wandb table name ('val' or 'test')
        """
        generations_to_log = self.config.trainer.val_generations_to_log_to_wandb

        if generations_to_log == 0:
            return

        if generations_to_log > 0 and 'wandb' not in self.config.trainer.logger:
            print(
                f'WARNING: `val_generations_to_log_to_wandb` is set to a positive value, but no wandb logger is found. ')
            return

        import wandb
        import numpy as np

        # Create tuples of (input, output, score) and sort by input text
        samples = list(zip(inputs, outputs, scores))
        samples.sort(key=lambda x: x[0])  # Sort by input text

        # Use fixed random seed for deterministic shuffling
        rng = np.random.RandomState(42)
        rng.shuffle(samples)

        # Take first N samples after shuffling
        samples = samples[:generations_to_log]

        # Create column names for all samples
        columns = ["step"] + sum([[f"input_{i+1}", f"output_{i+1}", f"score_{i+1}"] for i in range(len(samples))], [])

        table_attr = f"{prefix}_table"
        if not hasattr(self, table_attr):
            # Initialize the table on first call
            setattr(self, table_attr, wandb.Table(columns=columns))

        # Get the current table
        current_table = getattr(self, table_attr)

        # Create a new table with same columns and existing data
        # Workaround for https://github.com/wandb/wandb/issues/2981#issuecomment-1997445737
        new_table = wandb.Table(columns=columns, data=current_table.data)

        # Add new row with all data
        row_data = []
        row_data.append(self.global_steps)
        for sample in samples:
            row_data.extend(sample)

        new_table.add_data(*row_data)

        # Update reference and log
        wandb.log({f"{prefix}/generations": new_table}, step=self.global_steps)
        setattr(self, table_attr, new_table)

    def _maybe_log_val_generations_to_wandb(self, inputs, outputs, scores):
        """Log a table of validation samples to wandb"""
        return self._maybe_log_generations_to_wandb(inputs, outputs, scores, prefix="val")

    def _evaluate_dataset(self, dataloader, prefix="val"):
        """Common evaluation function for both validation and test datasets

        Args:
            dataloader: The dataloader to evaluate
            prefix: Prefix for metrics ('val' or 'test')

        Returns:
            Dictionary of metrics
        """
        reward_tensor_lst = []
        reward_extra_info_dict: Dict[str,
                                     list[list[float]]] = None  # the values are of shape (num_of_batch, batch_size)
        data_source_lst = []

        # For curriculum learning
        data_source_rewards = {}
        data_source_advantages = {}

        # Lists to collect samples for the table
        sample_inputs = []
        sample_outputs = []
        sample_scores = []

        for test_data in dataloader:
            test_batch = DataProto.from_single_dict(test_data)

            # we only do evaluation on rule-based rm
            if self.config.reward_model.enable and test_batch[0].non_tensor_batch['reward_model']['style'] == 'model':
                return {}

            # Store original inputs
            input_ids = test_batch.batch['input_ids']
            input_texts = [self.tokenizer.decode(ids, skip_special_tokens=True) for ids in input_ids]
            sample_inputs.extend(input_texts)

            test_gen_batch = test_batch.pop(['input_ids', 'attention_mask', 'position_ids'])
            test_gen_batch.meta_info = {
                'eos_token_id': self.tokenizer.eos_token_id,
                'pad_token_id': self.tokenizer.pad_token_id,
                'recompute_log_prob': False,
                'do_sample': False,
                'validate': True,
            }

            # pad to be divisible by dp_size
            test_gen_batch_padded, pad_size = pad_dataproto_to_divisor(test_gen_batch, self.actor_rollout_wg.world_size)
            test_output_gen_batch_padded = self.actor_rollout_wg.generate_sequences(test_gen_batch_padded)
            # unpad
            test_output_gen_batch = unpad_dataproto(test_output_gen_batch_padded, pad_size=pad_size)
            print(f'{prefix} generation end')

            # Store generated outputs
            output_ids = test_output_gen_batch.batch['responses']
            output_texts = [self.tokenizer.decode(ids, skip_special_tokens=True) for ids in output_ids]
            sample_outputs.extend(output_texts)

            test_batch = test_batch.union(test_output_gen_batch)

            # evaluate using reward_function
            reward_fn = self.val_reward_fn  # Use the same reward function for both validation and test
            reward_result = reward_fn(test_batch)

            # Handle both scalar and dictionary returns
            if isinstance(reward_result, dict):
                reward_tensor = reward_result['reward_tensor']
                if 'extra_info' in reward_result:
                    if reward_extra_info_dict is None:
                        reward_extra_info_dict = {}
                    for key, extra_reward in reward_result['extra_info'].items():
                        if key not in reward_extra_info_dict:
                            reward_extra_info_dict[key] = [extra_reward]
                        else:
                            reward_extra_info_dict[key].append(extra_reward)
            else:
                reward_tensor = reward_result

            # Store scores
            scores = reward_tensor.sum(-1).cpu().tolist()
            sample_scores.extend(scores)

            reward_tensor_lst.append(reward_tensor)

            # Process data_sources and collect rewards and advantages for curriculum learning
            current_data_sources = test_batch.non_tensor_batch.get('data_source', ['unknown'] * reward_tensor.shape[0])
            data_source_lst.append(current_data_sources)

            # For curriculum learning: collect rewards, advantages, log perplexity, and correctness by data source
            enable_curriculum_learning = hasattr(self, 'curriculum_sampler') and self.curriculum_sampler is not None
            if enable_curriculum_learning and prefix == "val":  # Only update curriculum weights from validation data
                responses = test_batch.batch['responses']
                response_length = responses.size(-1)
                attention_mask = test_batch.batch['attention_mask']
                response_mask = attention_mask[:, -response_length:].bool()
                rewards = reward_tensor

                # Compute advantages (simple version - using rewards as advantages)
                advantages = rewards

                # Calculate log perplexity if old_log_probs are available
                log_ppls = None
                if 'old_log_probs' in test_batch.batch:
                    log_probs = test_batch.batch['old_log_probs']
                    log_ppls = calculate_log_perplexity(log_probs, response_mask)

                # Collect by data source
                for i, source in enumerate(current_data_sources):
                    if source not in data_source_rewards:
                        data_source_rewards[source] = []
                        data_source_advantages[source] = []
                        if 'data_source_logppls' not in locals():
                            data_source_logppls = {}
                        if 'data_source_correctness' not in locals():
                            data_source_correctness = {}
                        data_source_logppls[source] = []
                        data_source_correctness[source] = []

                    # Get reward and advantage for this sample
                    sample_rewards = rewards[i].sum().item()
                    sample_advantages = advantages[i][response_mask[i]].mean().item()

                    # Calculate correctness (0-1) based on reward threshold
                    correctness = 1.0 if sample_rewards >= 3.0 else 0.0

                    # Add metrics to their respective dictionaries
                    data_source_rewards[source].append(sample_rewards)
                    data_source_advantages[source].append(sample_advantages)
                    data_source_correctness[source].append(correctness)

                    # Add log perplexity if available
                    if log_ppls is not None:
                        data_source_logppls[source].append(log_ppls[i].item())

        # Log sample generations to wandb
        self._maybe_log_generations_to_wandb(inputs=sample_inputs, outputs=sample_outputs, scores=sample_scores, prefix=prefix)

        reward_tensor = torch.cat(reward_tensor_lst, dim=0).sum(-1).cpu()  # (batch_size,)
        data_sources = np.concatenate(data_source_lst, axis=0)

        # evaluate score based on data source
        data_source_reward = {}
        for i in range(reward_tensor.shape[0]):
            data_source = data_sources[i]
            if data_source not in data_source_reward:
                data_source_reward[data_source] = []
            data_source_reward[data_source].append(reward_tensor[i].item())

        if reward_extra_info_dict is not None:
            data_source_reward_extra_info = {}
            for key, extra_info_lst in reward_extra_info_dict.items():
                data_source_reward_extra_info[key] = {}

                #fix bug: extra_info_lst is a list of list
                if isinstance(extra_info_lst, list) and len(extra_info_lst) == 1 and isinstance(extra_info_lst[0], list):
                    extra_info_lst = extra_info_lst[0]

                for i in range(len(extra_info_lst)):
                    data_source = data_sources[i]
                    if data_source not in data_source_reward_extra_info[key]:
                        data_source_reward_extra_info[key][data_source] = []
                    data_source_reward_extra_info[key][data_source].append(extra_info_lst[i])

        metric_dict = {}
        for data_source, rewards in data_source_reward.items():
            metric_dict[f'{prefix}/score/{data_source}'] = np.mean(rewards)
            # Track overall average across all data sources
            if f'{prefix}/score/overall' not in metric_dict:
                metric_dict[f'{prefix}/score/overall'] = []
            metric_dict[f'{prefix}/score/overall'].extend(rewards)

        if reward_extra_info_dict is not None:
            for key, extra_info_dict in data_source_reward_extra_info.items():
                for data_source, extra_info_lst in extra_info_dict.items():
                    metric_dict[f'{prefix}/score_extra/{data_source}/{key}'] = np.mean(extra_info_lst)

        # Update curriculum weights if curriculum learning is enabled (only for validation data)
        enable_curriculum_learning = hasattr(self, 'curriculum_sampler') and self.curriculum_sampler is not None
        if enable_curriculum_learning and prefix == "val" and data_source_rewards and data_source_advantages:
            print("Updating curriculum weights based on validation results")

            # Update the curriculum sampler with rewards, advantages, log perplexity, and correctness
            for source, rewards in data_source_rewards.items():
                advantages = data_source_advantages.get(source, [0.0])  # Default if missing

                # Get log perplexity and correctness if available
                logppls = data_source_logppls.get(source, []) if 'data_source_logppls' in locals() else []
                correctness = data_source_correctness.get(source, []) if 'data_source_correctness' in locals() else []

                # Calculate average values for this source
                avg_reward = np.mean(rewards)
                avg_advantage = np.mean(advantages)

                # Prepare update data
                update_data = {
                    'source_rewards': {source: [avg_reward]},
                    'source_advantages': {source: [avg_advantage]}
                }

                # Add log perplexity if available
                if logppls:
                    avg_logppl = np.mean(logppls)
                    update_data['source_logppls'] = {source: [avg_logppl]}

                # Add correctness if available
                if correctness:
                    avg_correctness = np.mean(correctness)
                    update_data['source_correctness'] = {source: [avg_correctness]}

                # Update curriculum sampler with this data
                self.curriculum_sampler.update_weights(**update_data)

            # After updating, get the current weights for logging
            current_weights = self.curriculum_sampler.get_source_stats()

            # Add curriculum learning stats to metrics
            for source, stats in current_weights.items():
                # Skip non-source keys like 'weight_trend'
                if source == 'weight_trend':
                    continue

                # Make sure stats is a dictionary with the expected keys
                if isinstance(stats, dict):
                    metric_dict[f'curriculum/expected_value/{source}'] = stats.get('expected_value', 0.0)
                    metric_dict[f'curriculum/reward_rate/{source}'] = stats.get('reward_rate', 0.0)
                    metric_dict[f'curriculum/sampling_weight/{source}'] = stats.get('sampling_weight', 0.0)
                    metric_dict[f'curriculum/norm_logppl/{source}'] = stats.get('norm_logppl', 0.1)
                    metric_dict[f'curriculum/correctness_rate/{source}'] = stats.get('correctness_rate', 0.1)
                else:
                    # Handle case where stats is not a dictionary (e.g., a numpy float)
                    print(f"Warning: stats for source {source} is not a dictionary: {type(stats)}")

            # Log overall curriculum stats - weight_trend is a top-level key in the dictionary
            if 'weight_trend' in current_weights:
                metric_dict['curriculum/weight_trend'] = current_weights['weight_trend']

            # Create and add visualization metrics for curriculum learning
            try:
                from verl.utils.curriculum_visualizer import create_curriculum_wandb_plots
                vis_metrics = create_curriculum_wandb_plots(current_weights, self.global_steps)
                metric_dict.update(vis_metrics)
            except ImportError:
                print("Warning: curriculum_visualizer not available, skipping visualizations")
            except Exception as e:
                print(f"Error creating curriculum visualizations: {e}")

            print(f"Updated curriculum weights: {current_weights}")

        # Calculate overall average using weighted mean based on sample counts
        if f'{prefix}/score/overall' in metric_dict:
            # Get sample counts for each data source
            source_sample_counts = {}
            total_samples = 0
            for data_source, rewards in data_source_reward.items():
                sample_count = len(rewards)
                source_sample_counts[data_source] = sample_count
                total_samples += sample_count

            # Calculate weighted average
            weighted_sum = 0.0
            for data_source, rewards in data_source_reward.items():
                source_weight = source_sample_counts[data_source] / total_samples
                source_mean = np.mean(rewards)
                weighted_sum += source_mean * source_weight

            metric_dict[f'{prefix}/score/overall'] = weighted_sum

        return metric_dict

    def _validate(self):
        """Run validation on the validation dataset"""
        return self._evaluate_dataset(self.val_dataloader, prefix="val")

    def _test(self):
        """Run evaluation on the test dataset"""
        if not hasattr(self, 'test_dataloader') or self.test_dataloader is None:
            print("No test dataset available, skipping test evaluation")
            return {}

        print("Running evaluation on test dataset")
        return self._evaluate_dataset(self.test_dataloader, prefix="test")

    def init_workers(self):
        """Init resource pool and worker group"""
        self.resource_pool_manager.create_resource_pool()

        self.resource_pool_to_cls = {pool: {} for pool in self.resource_pool_manager.resource_pool_dict.values()}

        # create actor and rollout
        if self.hybrid_engine:
            resource_pool = self.resource_pool_manager.get_resource_pool(Role.ActorRollout)
            actor_rollout_cls = RayClassWithInitArgs(cls=self.role_worker_mapping[Role.ActorRollout],
                                                     config=self.config.actor_rollout_ref,
                                                     role='actor_rollout')
            self.resource_pool_to_cls[resource_pool]['actor_rollout'] = actor_rollout_cls
        else:
            raise NotImplementedError

        # create critic
        if self.use_critic:
            resource_pool = self.resource_pool_manager.get_resource_pool(Role.Critic)
            critic_cls = RayClassWithInitArgs(cls=self.role_worker_mapping[Role.Critic], config=self.config.critic)
            self.resource_pool_to_cls[resource_pool]['critic'] = critic_cls

        # create reference policy if needed
        if self.use_reference_policy:
            resource_pool = self.resource_pool_manager.get_resource_pool(Role.RefPolicy)
            ref_policy_cls = RayClassWithInitArgs(self.role_worker_mapping[Role.RefPolicy],
                                                  config=self.config.actor_rollout_ref,
                                                  role='ref')
            self.resource_pool_to_cls[resource_pool]['ref'] = ref_policy_cls

        # create a reward model if reward_fn is None
        if self.use_rm:
            # we create a RM here
            resource_pool = self.resource_pool_manager.get_resource_pool(Role.RewardModel)
            rm_cls = RayClassWithInitArgs(self.role_worker_mapping[Role.RewardModel], config=self.config.reward_model)
            self.resource_pool_to_cls[resource_pool]['rm'] = rm_cls

        # initialize WorkerGroup
        # NOTE: if you want to use a different resource pool for each role, which can support different parallel size,
        # you should not use `create_colocated_worker_cls`. Instead, directly pass different resource pool to different worker groups.
        # See https://github.com/volcengine/verl/blob/master/examples/ray/tutorial.ipynb for more information.
        all_wg = {}
        self.wg_dicts = []
        for resource_pool, class_dict in self.resource_pool_to_cls.items():
            worker_dict_cls = create_colocated_worker_cls(class_dict=class_dict)
            wg_dict = self.ray_worker_group_cls(resource_pool=resource_pool, ray_cls_with_init=worker_dict_cls)
            spawn_wg = wg_dict.spawn(prefix_set=class_dict.keys())
            all_wg.update(spawn_wg)
            # keep the referece of WorkerDict to support ray >= 2.31. Ref: https://github.com/ray-project/ray/pull/45699
            self.wg_dicts.append(wg_dict)

        if self.use_critic:
            self.critic_wg = all_wg['critic']
            self.critic_wg.init_model()

        if self.use_reference_policy:
            self.ref_policy_wg = all_wg['ref']
            self.ref_policy_wg.init_model()

        if self.use_rm:
            self.rm_wg = all_wg['rm']
            self.rm_wg.init_model()

        # we should create rollout at the end so that vllm can have a better estimation of kv cache memory
        self.actor_rollout_wg = all_wg['actor_rollout']
        self.actor_rollout_wg.init_model()

    def _save_checkpoint(self):
        # path: given_path + `/global_step_{global_steps}` + `/actor`
        local_global_step_folder = os.path.join(self.config.trainer.default_local_dir,
                                                f'global_step_{self.global_steps}')
        actor_local_path = os.path.join(local_global_step_folder, 'actor')

        actor_remote_path = None if self.config.trainer.default_hdfs_dir is None else os.path.join(
            self.config.trainer.default_hdfs_dir, f'global_step_{self.global_steps}', 'actor')
        self.actor_rollout_wg.save_checkpoint(actor_local_path,
                                              actor_remote_path,
                                              self.global_steps,
                                              remove_previous_ckpt=self.config.trainer.remove_previous_ckpt_in_save)

        if self.use_critic:
            critic_local_path = os.path.join(local_global_step_folder, 'critic')
            critic_remote_path = None if self.config.trainer.default_hdfs_dir is None else os.path.join(
                self.config.trainer.default_hdfs_dir, f'global_step_{self.global_steps}', 'critic')
            self.critic_wg.save_checkpoint(critic_local_path,
                                           critic_remote_path,
                                           self.global_steps,
                                           remove_previous_ckpt=self.config.trainer.remove_previous_ckpt_in_save)

        # save dataloader
        dataloader_local_path = os.path.join(local_global_step_folder, 'data.pt')
        import dill
        torch.save(self.train_dataloader, dataloader_local_path, pickle_module=dill)

        # latest checkpointed iteration tracker (for atomic usage)
        local_latest_checkpointed_iteration = os.path.join(self.config.trainer.default_local_dir,
                                                           'latest_checkpointed_iteration.txt')
        with open(local_latest_checkpointed_iteration, 'w') as f:
            f.write(str(self.global_steps))

    def save_and_upload_checkpoint(self, upload_to_hf=False, hf_repo_id=None):
        """
        Save distributed checkpoint, merge it, and upload to Hugging Face
        """
        # 1. Save distributed checkpoint normally
        self._save_checkpoint()

        # 2. Execute merge and upload on the main process
        if upload_to_hf:
            import subprocess
            import os
            from datetime import datetime

            checkpoint_dir = os.path.join(self.config.trainer.default_local_dir, f"global_step_{self.global_steps}/actor")

            # Ensure repo_id is provided
            if not hf_repo_id:
                #timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                hf_repo_id = f"{self.config.trainer.hf_account}/{self.config.trainer.experiment_name}_global_step_{self.global_steps}"

            print(f"Starting model merge and HF upload for step {self.global_steps} to {hf_repo_id}")

            # Start merge and upload process
            merge_cmd = [
                "python", "scripts/model_merger.py",
                "--local_dir", checkpoint_dir,
                "--hf_upload_path", hf_repo_id
            ]

            try:
                # Execute merge and upload, wait for completion
                subprocess.run(merge_cmd, check=True)
                print(f"Successfully merged and uploaded model to HF: {hf_repo_id}")

                # Optional: Delete original distributed checkpoints to save space
                if self.config.trainer.get("cleanup_after_upload", False):
                    cleanup_cmd = f"rm -rf {os.path.join(checkpoint_dir, 'model_world_size_*')}"
                    subprocess.run(cleanup_cmd, shell=True)

                    cleanup_cmd = f"rm -rf {os.path.join(checkpoint_dir, 'optim_world_size_*')}"
                    subprocess.run(cleanup_cmd, shell=True)

                    cleanup_cmd = f"rm -rf {os.path.join(checkpoint_dir, 'extra_state_world_size_*')}"
                    subprocess.run(cleanup_cmd, shell=True)

                    print(f"Cleaned up distributed checkpoints at {checkpoint_dir}")

            except subprocess.CalledProcessError as e:
                print(f"Error during model merge and upload: {e}")

        #torch.distributed.barrier()

    def _load_checkpoint(self):
        if self.config.trainer.resume_mode == 'disable':
            return 0

        # load from hdfs
        if self.config.trainer.default_hdfs_dir is not None:
            NotImplementedError('load from hdfs is not implemented yet')
        else:
            checkpoint_folder = self.config.trainer.default_local_dir  # TODO: check path
            if not os.path.isabs(checkpoint_folder):
                working_dir = os.getcwd()
                checkpoint_folder = os.path.join(working_dir, checkpoint_folder)
            global_step_folder = find_latest_ckpt_path(checkpoint_folder)  # None if no latest

        # find global_step_folder
        if self.config.trainer.resume_mode == 'auto':
            if global_step_folder is None:
                print('Training from scratch')
                return 0
        else:
            if not (self.config.trainer.resume_from_path and global_step_folder is not None):
                assert isinstance(self.config.trainer.resume_mode, str), "resume ckpt must be str type"
                assert 'global_step_' in self.config.trainer.resume_mode, "resume ckpt must specify the global_steps"
                global_step_folder = self.config.trainer.resume_mode
                if not os.path.isabs(global_step_folder):
                    working_dir = os.getcwd()
                    global_step_folder = os.path.join(working_dir, global_step_folder)
        print(f'Load from checkpoint folder: {global_step_folder}')
        # set global step
        self.global_steps = int(global_step_folder.split('global_step_')[-1])

        print(f'Setting global step to {self.global_steps}')
        print(f'Resuming from {global_step_folder}')

        actor_path = os.path.join(global_step_folder, 'actor')
        critic_path = os.path.join(global_step_folder, 'critic')
        # load actor
        self.actor_rollout_wg.load_checkpoint(actor_path,
                                              del_local_after_load=self.config.trainer.del_local_ckpt_after_load)
        # load critic
        if self.use_critic:
            self.critic_wg.load_checkpoint(critic_path,
                                           del_local_after_load=self.config.trainer.del_local_ckpt_after_load)

        # load dataloader,
        # TODO: from remote not implemented yet
        dataloader_local_path = os.path.join(global_step_folder, 'data.pt')
        self.train_dataloader = torch.load(dataloader_local_path)
        if isinstance(self.train_dataloader.dataset, RLHFDataset):
            self.train_dataloader.dataset.resume_dataset_state()

    def _balance_batch(self, batch: DataProto, metrics, logging_prefix='global_seqlen'):
        """Reorder the data on single controller such that each dp rank gets similar total tokens"""
        attention_mask = batch.batch['attention_mask']
        batch_size = attention_mask.shape[0]
        global_seqlen_lst = batch.batch['attention_mask'].view(batch_size, -1).sum(-1).tolist()  # (train_batch_size,)
        world_size = self.actor_rollout_wg.world_size
        global_partition_lst = get_seqlen_balanced_partitions(global_seqlen_lst,
                                                              k_partitions=world_size,
                                                              equal_size=True)
        # reorder based on index. The data will be automatically equally partitioned by dispatch function
        global_idx = torch.tensor([j for partition in global_partition_lst for j in partition])
        batch.reorder(global_idx)
        global_balance_stats = log_seqlen_unbalance(seqlen_list=global_seqlen_lst,
                                                    partitions=global_partition_lst,
                                                    prefix=logging_prefix)
        metrics.update(global_balance_stats)

    def fit(self):
        """
        The training loop of PPO.
        The driver process only need to call the compute functions of the worker group through RPC to construct the PPO dataflow.
        The light-weight advantage computation is done on the driver process.
        """
        from verl.utils.tracking import Tracking
        from omegaconf import OmegaConf

        logger = Tracking(project_name=self.config.trainer.project_name,
                          experiment_name=self.config.trainer.experiment_name,
                          default_backend=self.config.trainer.logger,
                          config=OmegaConf.to_container(self.config, resolve=True))

        # Initialize curriculum learning visualizer if needed
        self.curriculum_visualizer = None
        if hasattr(self, 'curriculum_sampler') and self.curriculum_sampler is not None:
            try:
                from verl.utils.curriculum_visualizer import CurriculumVisualizer
                self.curriculum_visualizer = CurriculumVisualizer()
                print("Initialized curriculum learning visualizer for enhanced tracking")
            except ImportError:
                print("Warning: curriculum_visualizer not available, skipping curriculum visualization initialization")

        self.global_steps = 0

        # load checkpoint before doing anything
        self._load_checkpoint()

        if self.config.trainer.start_step:
            self.global_steps = int(self.config.trainer.start_step)
        # perform validation before training
        # currently, we only support validation using the reward_function.
        if self.val_reward_fn is not None and self.config.trainer.get('val_before_train', True):
            val_metrics = self._validate()
            pprint(f'Initial validation metrics: {val_metrics}')
            logger.log(data=val_metrics, step=self.global_steps)
            if self.config.trainer.get('val_only', False):
                return

        # we start from step 1
        self.global_steps += 1

        for epoch in range(self.config.trainer.total_epochs):
            for batch_dict in self.train_dataloader:
                metrics = {}
                timing_raw = {}

                batch: DataProto = DataProto.from_single_dict(batch_dict)

                # Add curriculum learning specific information to metrics
                if hasattr(self, 'curriculum_sampler') and self.curriculum_sampler is not None:
                    # Update source selection history with current step
                    # Find the most recent selection (which has placeholder step -1) and update it
                    if hasattr(self.curriculum_sampler, 'source_selection_history'):
                        selection_history = self.curriculum_sampler.source_selection_history
                        if selection_history['steps'] and selection_history['steps'][-1] == -1:
                            selection_history['steps'][-1] = self.global_steps

                    # Log current sampling weights
                    source_stats = self.curriculum_sampler.curriculum_controller.get_source_stats()

                    # Add basic metrics for each source
                    for source, stats in source_stats.items():
                        # Skip special keys
                        if source in ['weight_trend', 'source_selection_history']:
                            if source == 'weight_trend':
                                metrics['curriculum/weight_trend'] = stats
                            continue

                        if isinstance(stats, dict):
                            metrics[f'curriculum/expected_value/{source}'] = stats.get('expected_value', 0.0)
                            metrics[f'curriculum/reward_rate/{source}'] = stats.get('reward_rate', 0.0)
                            metrics[f'curriculum/sampling_weight/{source}'] = stats.get('sampling_weight', 0.0)
                            metrics[f'curriculum/sample_count/{source}'] = stats.get('n_samples', 0)
                            metrics[f'curriculum/norm_logppl/{source}'] = stats.get('norm_logppl', 0.1)
                            metrics[f'curriculum/correctness_rate/{source}'] = stats.get('correctness_rate', 0.1)

                    # Generate visualizations every step
                    if self.curriculum_visualizer is not None:
                        try:
                            # Update visualizer history
                            self.curriculum_visualizer.update_history(self.global_steps, source_stats)

                            # Create visualizations
                            vis_metrics = self.curriculum_visualizer.create_visualizations()
                            metrics.update(vis_metrics)
                        except Exception as e:
                            print(f"Error updating curriculum visualizations: {e}")

                # pop those keys for generation
                gen_batch = batch.pop(batch_keys=['input_ids', 'attention_mask', 'position_ids'])

                with _timer('step', timing_raw):
                    # generate a batch
                    with _timer('gen', timing_raw):
                        gen_batch_output = self.actor_rollout_wg.generate_sequences(gen_batch)

                    if self.config.algorithm.adv_estimator == 'remax':
                        with _timer('gen_max', timing_raw):
                            gen_baseline_batch = deepcopy(gen_batch)
                            gen_baseline_batch.meta_info['do_sample'] = False
                            gen_baseline_output = self.actor_rollout_wg.generate_sequences(gen_baseline_batch)

                            batch = batch.union(gen_baseline_output)
                            reward_baseline_tensor = self.reward_fn(batch)
                            reward_baseline_tensor = reward_baseline_tensor.sum(dim=-1)

                            batch.pop(batch_keys=list(gen_baseline_output.batch.keys()))

                            batch.batch['reward_baselines'] = reward_baseline_tensor

                            del gen_baseline_batch, gen_baseline_output

                    batch.non_tensor_batch['uid'] = np.array([str(uuid.uuid4()) for _ in range(len(batch.batch))],
                                                             dtype=object)
                    # repeat to align with repeated responses in rollout
                    batch = batch.repeat(repeat_times=self.config.actor_rollout_ref.rollout.n, interleave=True)
                    batch = batch.union(gen_batch_output)

                    # balance the number of valid tokens on each dp rank.
                    # Note that this breaks the order of data inside the batch.
                    # Please take care when you implement group based adv computation such as GRPO and rloo
                    self._balance_batch(batch, metrics=metrics)

                    # compute global_valid tokens
                    batch.meta_info['global_token_num'] = torch.sum(batch.batch['attention_mask'], dim=-1).tolist()

                    # recompute old_log_probs
                    with _timer('old_log_prob', timing_raw):
                        old_log_prob = self.actor_rollout_wg.compute_log_prob(batch)
                        batch = batch.union(old_log_prob)

                    if self.use_reference_policy:
                        # compute reference log_prob
                        with _timer('ref', timing_raw):
                            ref_log_prob = self.ref_policy_wg.compute_ref_log_prob(batch)
                            batch = batch.union(ref_log_prob)

                    # compute values
                    if self.use_critic:
                        with _timer('values', timing_raw):
                            values = self.critic_wg.compute_values(batch)
                            batch = batch.union(values)

                    with _timer('adv', timing_raw):
                        # compute scores. Support both model and function-based.
                        # We first compute the scores using reward model. Then, we call reward_fn to combine
                        # the results from reward model and rule-based results.
                        if self.use_rm:
                            # we first compute reward model score
                            reward_tensor = self.rm_wg.compute_rm_score(batch)
                            batch = batch.union(reward_tensor)

                        # we combine with rule-based rm
                        reward_result = self.reward_fn(batch)
                        extra_rewards_info = None
                        if isinstance(reward_result, dict):
                            batch.batch['token_level_scores'] = reward_result['reward_tensor']
                            if 'extra_info' in reward_result:
                                extra_rewards_info = reward_result['extra_info']
                        else:
                            batch.batch['token_level_scores'] = reward_result
                        # compute rewards. apply_kl_penalty if available
                        if not self.config.actor_rollout_ref.actor.get('use_kl_loss', False):
                            batch, kl_metrics = apply_kl_penalty(batch,
                                                                 kl_ctrl=self.kl_ctrl,
                                                                 kl_penalty=self.config.algorithm.kl_penalty)
                            metrics.update(kl_metrics)
                        else:
                            batch.batch['token_level_rewards'] = batch.batch['token_level_scores']

                        # compute advantages, executed on the driver process
                        batch = compute_advantage(batch,
                                                  adv_estimator=self.config.algorithm.adv_estimator,
                                                  gamma=self.config.algorithm.gamma,
                                                  lam=self.config.algorithm.lam,
                                                  num_repeat=self.config.actor_rollout_ref.rollout.n)

                        # Update curriculum weights using batch data
                        if hasattr(self, 'curriculum_sampler') and self.curriculum_sampler is not None:
                            # Collect rewards, advantages, log perplexity, correctness, and influence scores by data source
                            data_source_rewards = {}
                            data_source_advantages = {}
                            data_source_logppls = {}
                            data_source_correctness = {}
                            data_source_influence_scores = {}

                            current_data_sources = batch.non_tensor_batch.get('data_source', ['unknown'] * len(batch.batch['token_level_rewards']))
                            rewards = batch.batch['token_level_rewards']
                            advantages = batch.batch['advantages']

                            # Get response mask for token-level metrics
                            responses = batch.batch['responses']
                            response_length = responses.size(-1)
                            attention_mask = batch.batch['attention_mask']
                            response_mask = attention_mask[:, -response_length:].bool()

                            # Calculate log perplexity if old_log_probs are available
                            log_ppls = None
                            if 'old_log_probs' in batch.batch:
                                log_probs = batch.batch['old_log_probs']
                                log_ppls = calculate_log_perplexity(log_probs, response_mask)

                            # Compute influence scores if enabled
                            influence_scores = None
                            if hasattr(self, 'influence_calculator') and self.influence_calculator is not None:
                                try:
                                    # Initialize influence calculator with model if not done yet
                                    if not hasattr(self.influence_calculator, 'model'):
                                        # Get the model from actor worker group
                                        model = self.actor_rollout_wg.get_model()  # This method needs to be implemented
                                        self.influence_calculator = create_influence_calculator(model, self.influence_config)
                                        # Enable influence functions in curriculum sampler
                                        self.curriculum_sampler.enable_influence_functions(True)
                                        print("Influence function calculator initialized with model")

                                    # Compute influence scores for this batch
                                    # Note: This is a simplified version - in practice, you'd want to compute
                                    # influence scores more efficiently and possibly cache validation gradients
                                    batch_influence_scores = []
                                    for i in range(len(current_data_sources)):
                                        # Create single sample batch for influence computation
                                        sample_batch = {
                                            'input_ids': batch.batch['input_ids'][i:i+1],
                                            'attention_mask': batch.batch['attention_mask'][i:i+1],
                                            'responses': batch.batch['responses'][i:i+1]
                                        }

                                        # Use validation batch for influence computation
                                        # In practice, you'd cache this validation batch
                                        val_batch = self._get_validation_batch_for_influence()

                                        # Define loss function for influence computation
                                        def loss_fn(model_output, batch_data):
                                            # Simplified loss function - you may want to use the actual PPO loss
                                            logits = model_output.logits
                                            labels = batch_data['responses']
                                            return torch.nn.functional.cross_entropy(
                                                logits.view(-1, logits.size(-1)),
                                                labels.view(-1),
                                                ignore_index=-100
                                            )

                                        # Compute influence score
                                        influence_score = self.influence_calculator.compute_influence_score(
                                            sample_batch, val_batch, loss_fn
                                        )
                                        batch_influence_scores.append(influence_score)

                                    influence_scores = torch.tensor(batch_influence_scores)
                                    print(f"Computed influence scores for batch: mean={influence_scores.mean():.4f}")

                                except Exception as e:
                                    print(f"Warning: Failed to compute influence scores: {e}")
                                    influence_scores = None

                            # Process each sample in the batch
                            for i, source in enumerate(current_data_sources):
                                if source not in data_source_rewards:
                                    data_source_rewards[source] = []
                                    data_source_advantages[source] = []
                                    data_source_logppls[source] = []
                                    data_source_correctness[source] = []
                                    data_source_influence_scores[source] = []

                                # Aggregate rewards and advantages for this sample
                                sample_rewards = rewards[i].sum().item()
                                sample_advantages = advantages[i][response_mask[i]].mean().item()

                                # Calculate correctness (0-1) based on reward threshold
                                # Consider a sample correct if reward is above a threshold (e.g., 3.0)
                                correctness = 1.0 if sample_rewards >= 3.0 else 0.0

                                # Add metrics to their respective dictionaries
                                data_source_rewards[source].append(sample_rewards)
                                data_source_advantages[source].append(sample_advantages)
                                data_source_correctness[source].append(correctness)

                                # Add log perplexity if available
                                if log_ppls is not None:
                                    data_source_logppls[source].append(log_ppls[i].item())

                                # Add influence score if available
                                if influence_scores is not None:
                                    data_source_influence_scores[source].append(influence_scores[i].item())

                            # Update curriculum sampler with all available data
                            if data_source_rewards and data_source_advantages:
                                self.curriculum_sampler.update_weights(
                                    source_rewards=data_source_rewards,
                                    source_advantages=data_source_advantages,
                                    source_logppls=data_source_logppls if log_ppls is not None else None,
                                    source_correctness=data_source_correctness,
                                    source_influence_scores=data_source_influence_scores if influence_scores is not None else None
                                )

                    # update critic
                    if self.use_critic:
                        with _timer('update_critic', timing_raw):
                            critic_output = self.critic_wg.update_critic(batch)
                        critic_output_metrics = reduce_metrics(critic_output.meta_info['metrics'])
                        metrics.update(critic_output_metrics)

                    # implement critic warmup
                    if self.config.trainer.critic_warmup <= self.global_steps:
                        # update actor
                        with _timer('update_actor', timing_raw):
                            actor_output = self.actor_rollout_wg.update_actor(batch)
                        actor_output_metrics = reduce_metrics(actor_output.meta_info['metrics'])
                        metrics.update(actor_output_metrics)

                    # validate
                    if self.val_reward_fn is not None and self.config.trainer.test_freq > 0 and \
                        self.global_steps % self.config.trainer.test_freq == 0:
                        with _timer('testing', timing_raw):
                            val_metrics: dict = self._validate()
                        metrics.update(val_metrics)

                    if self.config.trainer.save_freq > 0 and \
                            self.global_steps % self.config.trainer.save_freq == 0:
                        with _timer('save_checkpoint', timing_raw):
                            #self._save_checkpoint()
                            self.save_and_upload_checkpoint(upload_to_hf=True, hf_repo_id=None)

                # collect metrics
                metrics.update(compute_data_metrics(batch=batch, use_critic=self.use_critic))
                # Add extra rewards metrics if they exist
                if extra_rewards_info is not None:
                    for key, sequence_extra in extra_rewards_info.items():
                        metrics.update({
                            f'critic/extra_rewards/{key}/mean': np.mean(sequence_extra),
                            f'critic/extra_rewards/{key}/max': np.max(sequence_extra),
                            f'critic/extra_rewards/{key}/min': np.min(sequence_extra),
                        })

                metrics.update(compute_timing_metrics(batch=batch, timing_raw=timing_raw))

                # TODO: make a canonical logger that supports various backend
                logger.log(data=metrics, step=self.global_steps)

                self.global_steps += 1
                if self.progress_bar is not None:
                    self.progress_bar.update(1)

                if self.global_steps >= self.total_training_steps:
                    if self.progress_bar is not None:
                        self.progress_bar.close()

                    # perform validation after training
                    if self.val_reward_fn is not None:
                        val_metrics = self._validate()
                        pprint(f'Final validation metrics: {val_metrics}')
                        logger.log(data=val_metrics, step=self.global_steps)

                    # perform test evaluation after training if test dataset is available
                    if self.val_reward_fn is not None and hasattr(self, 'test_dataloader') and self.test_dataloader is not None:
                        with _timer('testing', timing_raw):
                            test_metrics = self._test()
                        pprint(f'Test metrics: {test_metrics}')
                        logger.log(data=test_metrics, step=self.global_steps)

                    if self.config.trainer.save_freq > 0 and \
                            (self.global_steps - 1) % self.config.trainer.save_freq != 0:
                        with _timer('save_checkpoint', timing_raw):
                            #self._save_checkpoint()
                            self.save_and_upload_checkpoint(upload_to_hf=True, hf_repo_id=None)
                    return


