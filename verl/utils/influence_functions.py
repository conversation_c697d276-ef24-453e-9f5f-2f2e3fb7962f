"""
Influence Function Computation Module

This module implements influence functions for curriculum learning, replacing perplexity-based
bandit updates with influence function calculations.

The influence function measures how a training sample z affects the loss on validation set D_r:
I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)

Where:
- I_θ(D_r, z) is the influence function
- L(θ, D_r) is the loss function evaluated on validation set D_r  
- L(θ, z) is the loss function evaluated on training sample z
- θ represents the model parameters
- H_θ is the Hessian matrix of the loss with respect to parameters
- λ is a regularization parameter
- I is the identity matrix
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


class InfluenceFunctionCalculator:
    """
    Calculator for influence functions with K-FAC Hessian approximation.
    
    This class computes influence scores that measure how training samples
    affect validation performance, replacing perplexity-based scoring.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 regularization_lambda: float = 1e-3,
                 damping_factor: float = 1e-3,
                 use_kfac: bool = True,
                 max_samples_per_batch: int = 32):
        """
        Initialize the influence function calculator.
        
        Args:
            model: The neural network model
            regularization_lambda: Regularization parameter λ for (H_θ + λI)^(-1)
            damping_factor: Damping factor for numerical stability
            use_kfac: Whether to use K-FAC approximation for Hessian
            max_samples_per_batch: Maximum samples to process in one batch
        """
        self.model = model
        self.regularization_lambda = regularization_lambda
        self.damping_factor = damping_factor
        self.use_kfac = use_kfac
        self.max_samples_per_batch = max_samples_per_batch
        
        # Cache for gradients and Hessian information
        self.validation_gradients = None
        self.hessian_info = None
        self.device = next(model.parameters()).device
        
    def compute_gradients(self, 
                         batch: Dict[str, torch.Tensor], 
                         loss_fn: callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients of the loss with respect to model parameters.
        
        Args:
            batch: Input batch containing input_ids, attention_mask, etc.
            loss_fn: Loss function that takes model output and returns loss
            
        Returns:
            Dictionary mapping parameter names to gradient tensors
        """
        self.model.zero_grad()
        
        # Forward pass
        outputs = self.model(**batch)
        loss = loss_fn(outputs, batch)
        
        # Backward pass to compute gradients
        loss.backward()
        
        # Collect gradients
        gradients = {}
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                gradients[name] = param.grad.clone().detach()
        
        return gradients
    
    def compute_validation_gradients(self, 
                                   validation_batch: Dict[str, torch.Tensor],
                                   loss_fn: callable) -> None:
        """
        Compute and cache gradients on validation set D_r.
        
        Args:
            validation_batch: Validation data batch
            loss_fn: Loss function
        """
        self.validation_gradients = self.compute_gradients(validation_batch, loss_fn)
        logger.debug(f"Computed validation gradients for {len(self.validation_gradients)} parameters")
    
    def flatten_gradients(self, gradients: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Flatten gradient dictionary into a single vector.
        
        Args:
            gradients: Dictionary of parameter gradients
            
        Returns:
            Flattened gradient vector
        """
        grad_list = []
        for name in sorted(gradients.keys()):
            grad_list.append(gradients[name].view(-1))
        return torch.cat(grad_list)
    
    def compute_hvp(self, 
                   vector: torch.Tensor,
                   batch: Dict[str, torch.Tensor],
                   loss_fn: callable) -> torch.Tensor:
        """
        Compute Hessian-vector product H_θ * vector efficiently.
        
        Uses the identity: H_θ * v = ∇_θ (∇_θ L(θ) · v)
        
        Args:
            vector: Vector to multiply with Hessian
            batch: Input batch for computing Hessian
            loss_fn: Loss function
            
        Returns:
            Hessian-vector product
        """
        # First compute gradients
        gradients = self.compute_gradients(batch, loss_fn)
        flat_gradients = self.flatten_gradients(gradients)
        
        # Compute gradient-vector product
        gvp = torch.sum(flat_gradients * vector)
        
        # Compute second-order gradients (Hessian-vector product)
        self.model.zero_grad()
        gvp.backward()
        
        hvp_list = []
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                hvp_list.append(param.grad.view(-1))
        
        return torch.cat(hvp_list)
    
    def solve_inverse_hvp(self, 
                         vector: torch.Tensor,
                         batch: Dict[str, torch.Tensor],
                         loss_fn: callable,
                         max_iterations: int = 100,
                         tolerance: float = 1e-6) -> torch.Tensor:
        """
        Solve (H_θ + λI)^(-1) * vector using conjugate gradient method.
        
        Args:
            vector: Vector to solve for
            batch: Batch for Hessian computation
            loss_fn: Loss function
            max_iterations: Maximum CG iterations
            tolerance: Convergence tolerance
            
        Returns:
            Solution to (H_θ + λI)^(-1) * vector
        """
        # Initialize solution
        x = torch.zeros_like(vector)
        r = vector.clone()
        p = r.clone()
        rsold = torch.sum(r * r)
        
        for i in range(max_iterations):
            # Compute Hessian-vector product with regularization
            hvp = self.compute_hvp(p, batch, loss_fn)
            hvp += self.regularization_lambda * p  # Add λI * p
            
            alpha = rsold / torch.sum(p * hvp)
            x += alpha * p
            r -= alpha * hvp
            rsnew = torch.sum(r * r)
            
            if torch.sqrt(rsnew) < tolerance:
                logger.debug(f"CG converged in {i+1} iterations")
                break
                
            beta = rsnew / rsold
            p = r + beta * p
            rsold = rsnew
        
        return x
    
    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score for a single training sample.
        
        Args:
            training_sample: Single training sample
            validation_batch: Validation batch for computing validation gradients
            loss_fn: Loss function
            
        Returns:
            Influence score (scalar)
        """
        # Compute validation gradients if not cached
        if self.validation_gradients is None:
            self.compute_validation_gradients(validation_batch, loss_fn)
        
        # Compute training sample gradients
        training_gradients = self.compute_gradients(training_sample, loss_fn)
        
        # Flatten gradients
        val_grad_flat = self.flatten_gradients(self.validation_gradients)
        train_grad_flat = self.flatten_gradients(training_gradients)
        
        # Solve (H_θ + λI)^(-1) * ∇_θ L(θ, z)
        inverse_hvp = self.solve_inverse_hvp(train_grad_flat, validation_batch, loss_fn)
        
        # Compute influence: -∇_θ L(θ, D_r) · (H_θ + λI)^(-1) ∇_θ L(θ, z)
        influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()
        
        print("成功计算影响函数")
        return influence_score

    def compute_influence_scores_batch(self,
                                     training_samples: List[Dict[str, torch.Tensor]],
                                     validation_batch: Dict[str, torch.Tensor],
                                     loss_fn: callable) -> List[float]:
        """
        Compute influence scores for a batch of training samples.

        Args:
            training_samples: List of training samples
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            List of influence scores
        """
        influence_scores = []

        # Process in smaller batches to manage memory
        for i in range(0, len(training_samples), self.max_samples_per_batch):
            batch_samples = training_samples[i:i + self.max_samples_per_batch]

            for sample in batch_samples:
                try:
                    score = self.compute_influence_score(sample, validation_batch, loss_fn)
                    influence_scores.append(score)
                except Exception as e:
                    logger.warning(f"Failed to compute influence score for sample {i}: {e}")
                    influence_scores.append(0.0)  # Default score on failure

        return influence_scores


class KFACInfluenceFunctionCalculator(InfluenceFunctionCalculator):
    """
    Influence function calculator with K-FAC (Kronecker-Factored Approximate Curvature)
    Hessian approximation for improved computational efficiency.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.kfac_factors = {}
        self.kfac_damping = kwargs.get('kfac_damping', 1e-3)

    def extract_kfac_factors(self, batch: Dict[str, torch.Tensor], loss_fn: callable):
        """
        Extract K-FAC factors for Kronecker product approximation of the Hessian.

        For linear layers: H ≈ A ⊗ G where A is input covariance, G is gradient covariance

        Args:
            batch: Input batch
            loss_fn: Loss function
        """
        self.kfac_factors = {}

        # Register hooks to capture activations and gradients
        def forward_hook(module, input, output):
            if isinstance(module, nn.Linear):
                # Store input activations for A factor
                activations = input[0].detach()
                if len(activations.shape) > 2:
                    activations = activations.view(-1, activations.shape[-1])

                # Add bias term if present
                if module.bias is not None:
                    ones = torch.ones(activations.shape[0], 1, device=activations.device)
                    activations = torch.cat([activations, ones], dim=1)

                self.kfac_factors[f"{id(module)}_A"] = torch.mm(activations.t(), activations) / activations.shape[0]

        def backward_hook(module, grad_input, grad_output):
            if isinstance(module, nn.Linear):
                # Store output gradients for G factor
                grad_out = grad_output[0].detach()
                if len(grad_out.shape) > 2:
                    grad_out = grad_out.view(-1, grad_out.shape[-1])

                self.kfac_factors[f"{id(module)}_G"] = torch.mm(grad_out.t(), grad_out) / grad_out.shape[0]

        # Register hooks
        hooks = []
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                hooks.append(module.register_forward_hook(forward_hook))
                hooks.append(module.register_backward_hook(backward_hook))

        try:
            # Forward and backward pass to collect factors
            outputs = self.model(**batch)
            loss = loss_fn(outputs, batch)
            loss.backward()
        finally:
            # Remove hooks
            for hook in hooks:
                hook.remove()

    def kfac_inverse_hvp(self, vector: torch.Tensor) -> torch.Tensor:
        """
        Compute (H + λI)^(-1) * vector using K-FAC approximation.

        Args:
            vector: Vector to multiply with inverse Hessian

        Returns:
            Result of (H + λI)^(-1) * vector
        """
        if not self.kfac_factors:
            logger.warning("K-FAC factors not computed, falling back to standard method")
            return vector / self.regularization_lambda

        result = torch.zeros_like(vector)
        param_idx = 0

        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue

            param_size = param.numel()
            param_vector = vector[param_idx:param_idx + param_size].view(param.shape)

            # Check if this parameter has K-FAC factors
            module_id = None
            for module in self.model.modules():
                if isinstance(module, nn.Linear) and param in [module.weight, module.bias]:
                    module_id = id(module)
                    break

            if module_id and f"{module_id}_A" in self.kfac_factors and f"{module_id}_G" in self.kfac_factors:
                # Apply K-FAC inverse
                A = self.kfac_factors[f"{module_id}_A"] + self.kfac_damping * torch.eye(
                    self.kfac_factors[f"{module_id}_A"].shape[0], device=self.device)
                G = self.kfac_factors[f"{module_id}_G"] + self.kfac_damping * torch.eye(
                    self.kfac_factors[f"{module_id}_G"].shape[0], device=self.device)

                # Compute Kronecker product inverse: (A ⊗ G)^(-1) = A^(-1) ⊗ G^(-1)
                A_inv = torch.inverse(A)
                G_inv = torch.inverse(G)

                if param_vector.dim() == 2:  # Weight matrix
                    result_param = torch.mm(torch.mm(G_inv, param_vector), A_inv)
                else:  # Bias vector
                    result_param = torch.mv(G_inv, param_vector)
            else:
                # Fallback to diagonal approximation
                result_param = param_vector / self.regularization_lambda

            result[param_idx:param_idx + param_size] = result_param.view(-1)
            param_idx += param_size

        return result

    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score using K-FAC approximation.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            Influence score
        """
        # Compute validation gradients if not cached
        if self.validation_gradients is None:
            self.compute_validation_gradients(validation_batch, loss_fn)

        # Extract K-FAC factors if using K-FAC
        if self.use_kfac:
            self.extract_kfac_factors(validation_batch, loss_fn)

        # Compute training sample gradients
        training_gradients = self.compute_gradients(training_sample, loss_fn)

        # Flatten gradients
        val_grad_flat = self.flatten_gradients(self.validation_gradients)
        train_grad_flat = self.flatten_gradients(training_gradients)

        # Apply K-FAC inverse or fallback to CG
        if self.use_kfac and self.kfac_factors:
            inverse_hvp = self.kfac_inverse_hvp(train_grad_flat)
        else:
            inverse_hvp = self.solve_inverse_hvp(train_grad_flat, validation_batch, loss_fn)

        # Compute influence score
        influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()

        print("成功计算影响函数fkac")
        return influence_score


def create_influence_calculator(model: nn.Module,
                              config: Optional[Dict] = None) -> InfluenceFunctionCalculator:
    """
    Factory function to create an influence function calculator.

    Args:
        model: Neural network model
        config: Configuration dictionary with influence function parameters

    Returns:
        InfluenceFunctionCalculator instance
    """
    if config is None:
        config = {}

    use_kfac = config.get('use_kfac', True)

    if use_kfac:
        return KFACInfluenceFunctionCalculator(
            model=model,
            regularization_lambda=config.get('regularization_lambda', 1e-3),
            damping_factor=config.get('damping_factor', 1e-3),
            use_kfac=True,
            max_samples_per_batch=config.get('max_samples_per_batch', 32),
            kfac_damping=config.get('kfac_damping', 1e-3)
        )
    else:
        return InfluenceFunctionCalculator(
            model=model,
            regularization_lambda=config.get('regularization_lambda', 1e-3),
            damping_factor=config.get('damping_factor', 1e-3),
            use_kfac=False,
            max_samples_per_batch=config.get('max_samples_per_batch', 32)
        )
